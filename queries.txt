# Google API Key (AIzaSy) 泄露检测查询集合
# 使用说明：每行一个查询，可用于GitHub搜索或其他代码搜索引擎
# 优选策略：基于实际检测效果和准确性筛选的高价值查询

# === 核心基础搜索 (必备) ===
AIzaSyA in:file
AIzaSyA filename:.env
AIzaSyA filename:.env.example

# === 高精度配置文件搜索 (推荐) ===
AIzaSyA "API_KEY=" filename:.env
AIzaSyA "GOOGLE_API_KEY=" filename:.env
AIzaSyA "REACT_APP_GOOGLE_API_KEY=" filename:.env
AIzaSyA "VUE_APP_GOOGLE_API_KEY=" filename:.env
AIzaSyA filename:.env.local
AIzaSyA filename:.env.production
AIzaSyA filename:config extension:json
AIzaSyA filename:secrets.yml

# === 编程语言特定搜索 (高效) ===
AIzaSyA language:javascript "apiKey:"
AIzaSyA language:python "API_KEY ="
AIzaSyA language:java "final String API_KEY"
AIzaSyA extension:ipynb "api_key" "AIzaSyA"

# === Google服务上下文搜索 (精准) ===
AIzaSyA "maps.googleapis.com"
AIzaSyA "generativelanguage.googleapis.com"
AIzaSyA "fcm.googleapis.com"
"google.generativeai" "AIzaSyA" language:python
"GEMINI_API_KEY" "AIzaSyA"
AIzaSyA "gemini" filename:.env

# === CI/CD与基础设施搜索 (高风险) ===
AIzaSyA path:.github/workflows extension:yml
AIzaSyA filename:Dockerfile "ENV GOOGLE_API_KEY"
AIzaSyA filename:docker-compose.yml
AIzaSyA filename:terraform.tfvars
AIzaSyA kind:Secret extension:yaml

# === 移动应用特定搜索 (常见泄露点) ===
AIzaSyA filename:google-services.json
AIzaSyA filename:GoogleService-Info.plist
AIzaSyA path:android/app extension:json

# === 高级排除干扰搜索 (精确) ===
extension:json "api_key" "AIzaSyA" NOT "example" NOT "YOUR_API_KEY" NOT "placeholder"
AIzaSyA "Authorization: Bearer"
extension:diff "+*API_KEY*AIzaSyA"

# === 协作平台元数据搜索 (意外发现) ===
AIzaSyA in:comments
AIzaSyA in:issues
AIzaSyA "removed api key" in:commit

# === 冷门但高价值搜索 (补充) ===
AIzaSyA extension:log "key=AIzaSyA"
AIzaSyA filename:*.sql "INSERT INTO" "AIzaSyA"
AIzaSyA extension:bak OR extension:old
AIzaSyA "base64" "AIzaSyA"

# === Web框架配置搜索 (现代开发) ===
AIzaSyA filename:next.config.js
AIzaSyA filename:nuxt.config.js
AIzaSyA filename:webpack.config.js

# === 时间敏感搜索 (最新泄露) ===
AIzaSyA pushed:>2024-01-01
AIzaSyA created:>2024-01-01

# === 特定模式搜索 (常见编码方式) ===
AIzaSyA "process.env.GOOGLE_API_KEY"
AIzaSyA "os.environ.get"
