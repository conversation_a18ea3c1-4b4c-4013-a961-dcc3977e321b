#!/bin/bash

# Gemini-Keychecker集成安装脚本
# 用于将Gemini-Keychecker集成到hajimi-king项目中

set -e

echo "🚀 开始集成Gemini-Keychecker到hajimi-king项目..."

# 检查系统架构
ARCH=$(uname -m)
OS=$(uname -s)

if [[ "$OS" == "Linux" ]]; then
    if [[ "$ARCH" == "x86_64" ]]; then
        BINARY_NAME="gemini-keychecker-linux-x86_64"
    else
        echo "❌ 不支持的架构: $ARCH"
        exit 1
    fi
elif [[ "$OS" == "Darwin" ]]; then
    # macOS (如果有的话)
    echo "❌ 暂不支持macOS，请手动编译"
    exit 1
else
    echo "❌ 不支持的操作系统: $OS"
    exit 1
fi

# 下载Gemini-Keychecker
echo "📥 下载Gemini-Keychecker..."
DOWNLOAD_URL="https://github.com/Yoo1tic/Gemini-Keychecker/releases/latest/download/$BINARY_NAME"

if command -v wget >/dev/null 2>&1; then
    wget -O gemini-keychecker "$DOWNLOAD_URL"
elif command -v curl >/dev/null 2>&1; then
    curl -L -o gemini-keychecker "$DOWNLOAD_URL"
else
    echo "❌ 需要wget或curl来下载文件"
    exit 1
fi

# 设置执行权限
chmod +x gemini-keychecker

# 验证安装
echo "🔍 验证安装..."
if ./gemini-keychecker --help >/dev/null 2>&1; then
    echo "✅ Gemini-Keychecker安装成功"
else
    echo "❌ Gemini-Keychecker安装失败"
    exit 1
fi

# 更新配置文件
echo "⚙️ 更新配置文件..."
if [[ -f ".env" ]]; then
    # 检查是否已存在配置
    if grep -q "KEYCHECKER_ENABLED" .env; then
        echo "ℹ️ 配置已存在，跳过更新"
    else
        echo "" >> .env
        echo "# Gemini-Keychecker集成配置" >> .env
        echo "KEYCHECKER_ENABLED=true" >> .env
        echo "KEYCHECKER_PATH=./gemini-keychecker" >> .env
        echo "✅ 配置文件已更新"
    fi
else
    echo "⚠️ 未找到.env文件，请手动配置"
fi

echo ""
echo "🎉 集成完成！"
echo ""
echo "📋 使用说明："
echo "1. 确保.env文件中设置了 KEYCHECKER_ENABLED=true"
echo "2. 运行 python app/hajimi_king.py 开始使用"
echo "3. 现在将显示密钥层级信息："
echo "   - 💎 PAID: 付费密钥"
echo "   - 🆓 FREE: 免费密钥"
echo ""
echo "🔧 如需禁用Keychecker，设置 KEYCHECKER_ENABLED=false"
